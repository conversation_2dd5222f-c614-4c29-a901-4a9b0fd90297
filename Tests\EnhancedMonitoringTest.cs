using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Services;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat.Tests;

public class EnhancedMonitoringTest
{
    private readonly ILogger<EnhancedMonitoringTest> _logger;
    private readonly IServiceProvider _serviceProvider;

    public EnhancedMonitoringTest()
    {
        // Setup dependency injection for testing
        var services = new ServiceCollection();
        
        // Add logging with debug level
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Debug);
        });
        
        // Add configuration with test notification settings
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false)
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Monitoring:NotificationChannels:Email:Enabled"] = "true",
                ["Monitoring:NotificationChannels:Email:SmtpServer"] = "smtp.gmail.com",
                ["Monitoring:NotificationChannels:Email:SmtpPort"] = "587",
                ["Monitoring:NotificationChannels:Email:Username"] = "<EMAIL>",
                ["Monitoring:NotificationChannels:Email:Password"] = "testpassword",
                ["Monitoring:NotificationChannels:Email:ToAddress"] = "<EMAIL>",
                ["Monitoring:NotificationChannels:SMS:Enabled"] = "true",
                ["Monitoring:NotificationChannels:SMS:Provider"] = "Twilio",
                ["Monitoring:NotificationChannels:SMS:ToNumber"] = "+**********",
                ["Monitoring:NotificationChannels:Slack:Enabled"] = "true",
                ["Monitoring:NotificationChannels:Slack:WebhookUrl"] = "https://hooks.slack.com/test"
            })
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // Add services
        services.AddScoped<IAlpacaService, AlpacaService>();
        services.AddScoped<IAdvancedRiskManager, AdvancedRiskManager>();
        services.AddScoped<IPositionManager, PositionManager>();
        services.AddScoped<IPerformanceAnalytics, PerformanceAnalytics>();
        services.AddScoped<IRealTimeMonitoringService, RealTimeMonitoringService>();
        services.AddScoped<INotificationService, NotificationService>();
        services.AddScoped<IRiskManager, RiskManager>();
        services.AddScoped<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
        services.AddScoped<IHistoricalDataService, HistoricalDataService>();
        
        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<EnhancedMonitoringTest>>();
    }

    public async Task RunEnhancedMonitoringTest()
    {
        Console.WriteLine("=== Enhanced Real-time Monitoring Test ===\n");

        try
        {
            // Test 1: Notification Service Configuration
            await TestNotificationServiceConfiguration();

            // Test 2: Real-time Monitoring Service
            await TestRealTimeMonitoringService();

            // Test 3: Alert System
            await TestAlertSystem();

            // Test 4: Health Checks
            await TestHealthChecks();

            // Test 5: Dashboard Data
            await TestDashboardData();

            // Test 6: System Metrics
            await TestSystemMetrics();

            Console.WriteLine("\n=== Enhanced Monitoring Test Completed Successfully ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Enhanced monitoring test failed: {ex.Message}");
            _logger.LogError(ex, "Enhanced monitoring test failed");
        }
    }

    private async Task TestNotificationServiceConfiguration()
    {
        Console.WriteLine("--- Testing Notification Service Configuration ---");
        
        var notificationService = _serviceProvider.GetRequiredService<INotificationService>();
        
        // Test configuration validation
        var isValid = await notificationService.ValidateNotificationConfigurationAsync();
        Console.WriteLine($"Notification configuration validation: {(isValid ? "PASSED" : "FAILED")}");
        
        // Test available channels
        var channels = await notificationService.GetAvailableChannelsAsync();
        Console.WriteLine($"Available notification channels: {channels.Count}");
        
        foreach (var channel in channels)
        {
            Console.WriteLine($"  - {channel.Type}: Enabled={channel.IsEnabled}, Priority={channel.Priority}");
        }
        
        // Test email configuration (simulated)
        Console.WriteLine("Testing email configuration...");
        try
        {
            // Note: This would fail in real environment without valid SMTP settings
            // var emailTest = await notificationService.TestEmailConfigurationAsync();
            // Console.WriteLine($"Email test: {(emailTest ? "PASSED" : "FAILED")}");
            Console.WriteLine("Email test: SKIPPED (requires valid SMTP configuration)");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Email test: FAILED - {ex.Message}");
        }
        
        Console.WriteLine("Notification service configuration tests completed\n");
    }

    private async Task TestRealTimeMonitoringService()
    {
        Console.WriteLine("--- Testing Real-time Monitoring Service ---");
        
        var monitoringService = _serviceProvider.GetRequiredService<IRealTimeMonitoringService>();
        
        // Test service startup
        await monitoringService.StartMonitoringAsync();
        Console.WriteLine("Monitoring service started: PASSED");
        
        // Test alert configuration management
        var alertConfig = new AlertConfiguration
        {
            AlertType = "TestAlert",
            Threshold = 100m,
            IsEnabled = true,
            NotificationChannels = new List<string> { "Console" },
            CooldownPeriod = TimeSpan.FromMinutes(5)
        };
        
        var addResult = await monitoringService.AddAlertConfigurationAsync(alertConfig);
        Console.WriteLine($"Add alert configuration: {(addResult ? "PASSED" : "FAILED")}");
        
        var configs = await monitoringService.GetAlertConfigurationsAsync();
        Console.WriteLine($"Retrieved {configs.Count} alert configurations");
        
        var removeResult = await monitoringService.RemoveAlertConfigurationAsync("TestAlert");
        Console.WriteLine($"Remove alert configuration: {(removeResult ? "PASSED" : "FAILED")}");
        
        // Test service shutdown
        await monitoringService.StopMonitoringAsync();
        Console.WriteLine("Monitoring service stopped: PASSED");
        
        Console.WriteLine("Real-time monitoring service tests completed\n");
    }

    private async Task TestAlertSystem()
    {
        Console.WriteLine("--- Testing Alert System ---");
        
        var monitoringService = _serviceProvider.GetRequiredService<IRealTimeMonitoringService>();
        
        // Create a test alert configuration
        var alertConfig = new AlertConfiguration
        {
            AlertType = "TestSystemAlert",
            Threshold = 0.01m, // Very low threshold to trigger easily
            IsEnabled = true,
            NotificationChannels = new List<string> { "Console" },
            CooldownPeriod = TimeSpan.FromSeconds(1)
        };
        
        await monitoringService.AddAlertConfigurationAsync(alertConfig);
        
        // Start monitoring to trigger alerts
        await monitoringService.StartMonitoringAsync();
        
        // Wait for potential alerts
        await Task.Delay(3000);
        
        var activeAlerts = await monitoringService.GetActiveAlertsAsync();
        Console.WriteLine($"Active alerts generated: {activeAlerts.Count}");
        
        foreach (var alert in activeAlerts.Take(3))
        {
            Console.WriteLine($"  Alert: {alert.Type} - {alert.Severity} - {alert.Message}");
            
            // Test alert acknowledgment
            var ackResult = await monitoringService.AcknowledgeAlertAsync(alert.Id);
            Console.WriteLine($"  Acknowledged: {(ackResult ? "PASSED" : "FAILED")}");
        }
        
        await monitoringService.StopMonitoringAsync();
        Console.WriteLine("Alert system tests completed\n");
    }

    private async Task TestHealthChecks()
    {
        Console.WriteLine("--- Testing Health Checks ---");
        
        var monitoringService = _serviceProvider.GetRequiredService<IRealTimeMonitoringService>();
        
        var healthResults = await monitoringService.GetHealthCheckResultsAsync();
        Console.WriteLine($"Health check results: {healthResults.Count}");
        
        foreach (var result in healthResults)
        {
            Console.WriteLine($"  {result.ServiceName}: {(result.IsHealthy ? "HEALTHY" : "UNHEALTHY")} - {result.Status}");
            if (!string.IsNullOrEmpty(result.Details))
            {
                Console.WriteLine($"    Details: {result.Details}");
            }
            if (result.ResponseTime.HasValue)
            {
                Console.WriteLine($"    Response Time: {result.ResponseTime.Value.TotalMilliseconds}ms");
            }
        }
        
        var healthyCount = healthResults.Count(r => r.IsHealthy);
        var healthPercentage = healthResults.Count > 0 ? (double)healthyCount / healthResults.Count : 0;
        Console.WriteLine($"Overall health: {healthPercentage:P0} ({healthyCount}/{healthResults.Count} services healthy)");
        
        Console.WriteLine("Health check tests completed\n");
    }

    private async Task TestDashboardData()
    {
        Console.WriteLine("--- Testing Dashboard Data ---");
        
        var monitoringService = _serviceProvider.GetRequiredService<IRealTimeMonitoringService>();
        
        var dashboardData = await monitoringService.GetLiveDashboardDataAsync();
        
        Console.WriteLine($"Dashboard data retrieved:");
        Console.WriteLine($"  Timestamp: {dashboardData.Timestamp:yyyy-MM-dd HH:mm:ss}");
        Console.WriteLine($"  Account Value: {dashboardData.AccountValue:C}");
        Console.WriteLine($"  Day P&L: {dashboardData.DayPnL:C}");
        Console.WriteLine($"  Unrealized P&L: {dashboardData.UnrealizedPnL:C}");
        Console.WriteLine($"  Active Positions: {dashboardData.ActivePositions}");
        Console.WriteLine($"  Live Positions: {dashboardData.Positions.Count}");
        Console.WriteLine($"  Active Alerts: {dashboardData.ActiveAlerts.Count}");
        Console.WriteLine($"  Pending Signals: {dashboardData.PendingSignals.Count}");
        
        if (dashboardData.CurrentMarket != null)
        {
            Console.WriteLine($"  Market Conditions:");
            Console.WriteLine($"    VIX Level: {dashboardData.CurrentMarket.VixLevel:F2}");
            Console.WriteLine($"    SPX Trend: {dashboardData.CurrentMarket.SpxTrend:F2}");
            Console.WriteLine($"    Market Session: {dashboardData.CurrentMarket.MarketSession}");
        }
        
        Console.WriteLine("Dashboard data tests completed\n");
    }

    private async Task TestSystemMetrics()
    {
        Console.WriteLine("--- Testing System Metrics ---");
        
        var monitoringService = _serviceProvider.GetRequiredService<IRealTimeMonitoringService>();
        
        var metrics = await monitoringService.GetSystemMetricsAsync();
        
        Console.WriteLine($"System metrics retrieved:");
        Console.WriteLine($"  Timestamp: {metrics.Timestamp:yyyy-MM-dd HH:mm:ss}");
        Console.WriteLine($"  CPU Usage: {metrics.CpuUsage:P1}");
        Console.WriteLine($"  Memory Usage: {metrics.MemoryUsage:P1}");
        Console.WriteLine($"  Network Bytes Received: {metrics.NetworkBytesReceived:N0}");
        Console.WriteLine($"  Network Bytes Sent: {metrics.NetworkBytesSent:N0}");
        Console.WriteLine($"  Active Connections: {metrics.ActiveConnections}");
        Console.WriteLine($"  Uptime: {metrics.Uptime}");
        
        if (metrics.CustomMetrics.Any())
        {
            Console.WriteLine($"  Custom Metrics:");
            foreach (var metric in metrics.CustomMetrics)
            {
                Console.WriteLine($"    {metric.Key}: {metric.Value}");
            }
        }
        
        Console.WriteLine("System metrics tests completed\n");
    }

    public static async Task RunEnhancedMonitoringTestStatic()
    {
        var test = new EnhancedMonitoringTest();
        await test.RunEnhancedMonitoringTest();
    }
}
