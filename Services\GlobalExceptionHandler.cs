using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Sockets;
using System.Text.Json;
using Alpaca.Markets;

namespace ZeroDateStrat.Services;

public interface IGlobalExceptionHandler
{
    Task<bool> HandleExceptionAsync(Exception exception, string context = "");
    Task<T?> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3, string context = "");
    Task ExecuteWithRetryAsync(Func<Task> operation, int maxRetries = 3, string context = "");
    Task<bool> IsRecoverableExceptionAsync(Exception exception);
    Task LogExceptionAsync(Exception exception, string context, ExceptionSeverity severity = ExceptionSeverity.Medium);
    Task<ExceptionStatistics> GetExceptionStatisticsAsync();
}

public class GlobalExceptionHandler : IGlobalExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger;
    private readonly List<ExceptionRecord> _exceptionHistory = new();
    private readonly Dictionary<Type, int> _exceptionCounts = new();
    private readonly object _lockObject = new();

    public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
    {
        _logger = logger;
    }

    public async Task<bool> HandleExceptionAsync(Exception exception, string context = "")
    {
        try
        {
            var severity = DetermineExceptionSeverity(exception);
            await LogExceptionAsync(exception, context, severity);
            
            // Record exception for statistics
            RecordException(exception, context);

            // Determine if the exception is recoverable
            var isRecoverable = await IsRecoverableExceptionAsync(exception);

            // Handle specific exception types
            switch (exception)
            {
                case HttpRequestException httpEx:
                    return await HandleHttpExceptionAsync(httpEx, context);

                case TimeoutException timeoutEx:
                    return await HandleTimeoutExceptionAsync(timeoutEx, context);

                case UnauthorizedAccessException authEx:
                    return await HandleAuthorizationExceptionAsync(authEx, context);

                case ArgumentException argEx:
                    return await HandleArgumentExceptionAsync(argEx, context);

                case InvalidOperationException opEx:
                    return await HandleInvalidOperationExceptionAsync(opEx, context);

                case SocketException sockEx:
                    return await HandleSocketExceptionAsync(sockEx, context);

                default:
                    return await HandleGenericExceptionAsync(exception, context);
            }
        }
        catch (Exception handlerEx)
        {
            // Fallback logging if exception handler itself fails
            _logger.LogCritical(handlerEx, "Exception handler failed while processing exception: {OriginalException}", 
                               exception.Message);
            return false;
        }
    }

    public async Task<T?> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3, string context = "")
    {
        var attempt = 0;
        Exception? lastException = null;

        while (attempt <= maxRetries)
        {
            try
            {
                var result = await operation();
                
                if (attempt > 0)
                {
                    _logger.LogInformation("Operation succeeded on attempt {Attempt} in context: {Context}", 
                                         attempt + 1, context);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                lastException = ex;
                attempt++;

                if (attempt <= maxRetries && await IsRecoverableExceptionAsync(ex))
                {
                    var delay = CalculateRetryDelay(attempt);
                    _logger.LogWarning("Operation failed on attempt {Attempt}/{MaxRetries} in context: {Context}. " +
                                     "Retrying in {Delay}ms. Error: {Error}", 
                                     attempt, maxRetries + 1, context, delay, ex.Message);
                    
                    await Task.Delay(delay);
                }
                else
                {
                    _logger.LogError(ex, "Operation failed permanently after {Attempts} attempts in context: {Context}", 
                                   attempt, context);
                    break;
                }
            }
        }

        if (lastException != null)
        {
            await HandleExceptionAsync(lastException, $"{context} (after {attempt} attempts)");
        }

        return default(T);
    }

    public async Task ExecuteWithRetryAsync(Func<Task> operation, int maxRetries = 3, string context = "")
    {
        await ExecuteWithRetryAsync(async () =>
        {
            await operation();
            return true;
        }, maxRetries, context);
    }

    public async Task<bool> IsRecoverableExceptionAsync(Exception exception)
    {
        return exception switch
        {
            // Network-related exceptions are usually recoverable
            HttpRequestException => true,
            TimeoutException => true,
            SocketException => true,

            // Authentication and authorization errors are not recoverable
            UnauthorizedAccessException => false,

            // Argument and validation errors are not recoverable
            ArgumentNullException => false,
            ArgumentException => false,

            // Other exceptions need case-by-case evaluation
            _ => false
        };
    }

    public async Task LogExceptionAsync(Exception exception, string context, ExceptionSeverity severity = ExceptionSeverity.Medium)
    {
        var logLevel = severity switch
        {
            ExceptionSeverity.Low => LogLevel.Warning,
            ExceptionSeverity.Medium => LogLevel.Error,
            ExceptionSeverity.High => LogLevel.Critical,
            _ => LogLevel.Error
        };

        var exceptionData = new
        {
            ExceptionType = exception.GetType().Name,
            Message = exception.Message,
            Context = context,
            Severity = severity.ToString(),
            StackTrace = exception.StackTrace,
            InnerException = exception.InnerException?.Message,
            Timestamp = DateTime.UtcNow
        };

        _logger.Log(logLevel, exception, 
                   "Exception in context '{Context}': {ExceptionType} - {Message}", 
                   context, exception.GetType().Name, exception.Message);

        // Log additional details at debug level
        _logger.LogDebug("Exception details: {ExceptionData}", JsonSerializer.Serialize(exceptionData));
    }

    public async Task<ExceptionStatistics> GetExceptionStatisticsAsync()
    {
        lock (_lockObject)
        {
            var now = DateTime.UtcNow;
            var last24Hours = _exceptionHistory.Where(e => e.Timestamp > now.AddHours(-24)).ToList();
            var lastHour = _exceptionHistory.Where(e => e.Timestamp > now.AddHours(-1)).ToList();

            return new ExceptionStatistics
            {
                TotalExceptions = _exceptionHistory.Count,
                ExceptionsLast24Hours = last24Hours.Count,
                ExceptionsLastHour = lastHour.Count,
                MostCommonExceptions = _exceptionCounts
                    .OrderByDescending(kvp => kvp.Value)
                    .Take(10)
                    .ToDictionary(kvp => kvp.Key.Name, kvp => kvp.Value),
                RecentExceptions = _exceptionHistory
                    .OrderByDescending(e => e.Timestamp)
                    .Take(20)
                    .ToList(),
                GeneratedAt = now
            };
        }
    }

    private async Task<bool> HandleSocketExceptionAsync(SocketException exception, string context)
    {
        _logger.LogWarning("Socket error in {Context}: {Message}", context, exception.Message);
        return true; // Usually recoverable with retry
    }

    private async Task<bool> HandleHttpExceptionAsync(HttpRequestException exception, string context)
    {
        _logger.LogWarning("HTTP request failed in {Context}: {Message}", context, exception.Message);
        return true; // Usually recoverable
    }

    private async Task<bool> HandleTimeoutExceptionAsync(TimeoutException exception, string context)
    {
        _logger.LogWarning("Operation timed out in {Context}: {Message}", context, exception.Message);
        return true; // Recoverable with retry
    }

    private async Task<bool> HandleAuthorizationExceptionAsync(UnauthorizedAccessException exception, string context)
    {
        _logger.LogError("Authorization failed in {Context}: {Message}", context, exception.Message);
        return false; // Not recoverable
    }

    private async Task<bool> HandleArgumentExceptionAsync(ArgumentException exception, string context)
    {
        _logger.LogError("Invalid argument in {Context}: {Message}", context, exception.Message);
        return false; // Not recoverable - indicates programming error
    }

    private async Task<bool> HandleInvalidOperationExceptionAsync(InvalidOperationException exception, string context)
    {
        _logger.LogError("Invalid operation in {Context}: {Message}", context, exception.Message);
        return false; // Usually not recoverable
    }

    private async Task<bool> HandleGenericExceptionAsync(Exception exception, string context)
    {
        _logger.LogError(exception, "Unhandled exception in {Context}", context);
        return false; // Conservative approach for unknown exceptions
    }

    private ExceptionSeverity DetermineExceptionSeverity(Exception exception)
    {
        return exception switch
        {
            UnauthorizedAccessException => ExceptionSeverity.High,
            ArgumentNullException => ExceptionSeverity.High,
            InvalidOperationException => ExceptionSeverity.Medium,
            TimeoutException => ExceptionSeverity.Low,
            HttpRequestException => ExceptionSeverity.Low,
            SocketException => ExceptionSeverity.Low,
            _ => ExceptionSeverity.Medium
        };
    }

    private void RecordException(Exception exception, string context)
    {
        lock (_lockObject)
        {
            var record = new ExceptionRecord
            {
                ExceptionType = exception.GetType(),
                Message = exception.Message,
                Context = context,
                Timestamp = DateTime.UtcNow
            };

            _exceptionHistory.Add(record);

            // Update exception counts
            if (_exceptionCounts.ContainsKey(exception.GetType()))
            {
                _exceptionCounts[exception.GetType()]++;
            }
            else
            {
                _exceptionCounts[exception.GetType()] = 1;
            }

            // Keep only last 1000 exceptions
            if (_exceptionHistory.Count > 1000)
            {
                _exceptionHistory.RemoveRange(0, _exceptionHistory.Count - 1000);
            }
        }
    }

    private int CalculateRetryDelay(int attempt)
    {
        // Exponential backoff with jitter
        var baseDelay = Math.Pow(2, attempt) * 1000; // 2^attempt seconds
        var jitter = new Random().Next(0, 1000); // Add up to 1 second jitter
        return (int)Math.Min(baseDelay + jitter, 30000); // Cap at 30 seconds
    }
}

public enum ExceptionSeverity
{
    Low,
    Medium,
    High
}

public class ExceptionRecord
{
    public Type ExceptionType { get; set; } = typeof(Exception);
    public string Message { get; set; } = string.Empty;
    public string Context { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

public class ExceptionStatistics
{
    public int TotalExceptions { get; set; }
    public int ExceptionsLast24Hours { get; set; }
    public int ExceptionsLastHour { get; set; }
    public Dictionary<string, int> MostCommonExceptions { get; set; } = new();
    public List<ExceptionRecord> RecentExceptions { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}
