using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Mail;
using System.Text;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface INotificationService
{
    Task<bool> SendEmailAlertAsync(RiskAlert alert);
    Task<bool> SendSmsAlertAsync(RiskAlert alert);
    Task<bool> SendSlackAlertAsync(RiskAlert alert);
    Task<bool> TestEmailConfigurationAsync();
    Task<bool> TestSmsConfigurationAsync();
    Task<List<NotificationChannel>> GetAvailableChannelsAsync();
    Task<bool> ValidateNotificationConfigurationAsync();
}

public class NotificationService : INotificationService
{
    private readonly ILogger<NotificationService> _logger;
    private readonly IConfiguration _configuration;

    public NotificationService(ILogger<NotificationService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<bool> SendEmailAlertAsync(RiskAlert alert)
    {
        try
        {
            var emailConfig = _configuration.GetSection("Monitoring:NotificationChannels:Email");
            
            if (!emailConfig.GetValue<bool>("Enabled", false))
            {
                _logger.LogDebug("Email notifications are disabled");
                return false;
            }

            var smtpServer = emailConfig.GetValue<string>("SmtpServer");
            var smtpPort = emailConfig.GetValue<int>("SmtpPort", 587);
            var username = emailConfig.GetValue<string>("Username");
            var password = emailConfig.GetValue<string>("Password");
            var toAddress = emailConfig.GetValue<string>("ToAddress");

            if (string.IsNullOrEmpty(smtpServer) || string.IsNullOrEmpty(username) || 
                string.IsNullOrEmpty(password) || string.IsNullOrEmpty(toAddress))
            {
                _logger.LogWarning("Email configuration is incomplete");
                return false;
            }

            using var client = new SmtpClient(smtpServer, smtpPort)
            {
                Credentials = new NetworkCredential(username, password),
                EnableSsl = true
            };

            var subject = $"Trading Alert: {alert.Type} - {alert.Severity}";
            var body = BuildEmailBody(alert);

            var message = new MailMessage(username, toAddress, subject, body)
            {
                IsBodyHtml = true
            };

            await client.SendMailAsync(message);
            _logger.LogInformation($"Email alert sent successfully for {alert.Type}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send email alert for {alert.Type}");
            return false;
        }
    }

    public async Task<bool> SendSmsAlertAsync(RiskAlert alert)
    {
        try
        {
            var smsConfig = _configuration.GetSection("Monitoring:NotificationChannels:SMS");
            
            if (!smsConfig.GetValue<bool>("Enabled", false))
            {
                _logger.LogDebug("SMS notifications are disabled");
                return false;
            }

            var provider = smsConfig.GetValue<string>("Provider", "Twilio");
            
            switch (provider.ToLower())
            {
                case "twilio":
                    return await SendTwilioSmsAsync(alert, smsConfig);
                case "aws":
                    return await SendAwsSmsAsync(alert, smsConfig);
                default:
                    _logger.LogWarning($"Unsupported SMS provider: {provider}");
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send SMS alert for {alert.Type}");
            return false;
        }
    }

    public async Task<bool> SendSlackAlertAsync(RiskAlert alert)
    {
        try
        {
            var slackConfig = _configuration.GetSection("Monitoring:NotificationChannels:Slack");
            
            if (!slackConfig.GetValue<bool>("Enabled", false))
            {
                _logger.LogDebug("Slack notifications are disabled");
                return false;
            }

            var webhookUrl = slackConfig.GetValue<string>("WebhookUrl");
            if (string.IsNullOrEmpty(webhookUrl))
            {
                _logger.LogWarning("Slack webhook URL is not configured");
                return false;
            }

            var payload = BuildSlackPayload(alert);
            
            using var client = new HttpClient();
            var content = new StringContent(payload, Encoding.UTF8, "application/json");
            var response = await client.PostAsync(webhookUrl, content);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation($"Slack alert sent successfully for {alert.Type}");
                return true;
            }
            else
            {
                _logger.LogWarning($"Failed to send Slack alert. Status: {response.StatusCode}");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send Slack alert for {alert.Type}");
            return false;
        }
    }

    public async Task<bool> TestEmailConfigurationAsync()
    {
        try
        {
            var testAlert = new RiskAlert
            {
                Id = "TEST",
                Type = "ConfigurationTest",
                Severity = RiskLevel.Low,
                Message = "This is a test email from the Zero DTE Trading System",
                Timestamp = DateTime.UtcNow,
                Value = 0,
                Threshold = 0
            };

            return await SendEmailAlertAsync(testAlert);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test email configuration");
            return false;
        }
    }

    public async Task<bool> TestSmsConfigurationAsync()
    {
        try
        {
            var testAlert = new RiskAlert
            {
                Id = "TEST",
                Type = "ConfigurationTest",
                Severity = RiskLevel.Low,
                Message = "Test SMS from Zero DTE Trading System",
                Timestamp = DateTime.UtcNow,
                Value = 0,
                Threshold = 0
            };

            return await SendSmsAlertAsync(testAlert);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test SMS configuration");
            return false;
        }
    }

    public async Task<List<NotificationChannel>> GetAvailableChannelsAsync()
    {
        await Task.CompletedTask;
        
        var channels = new List<NotificationChannel>();
        
        // Check Email
        var emailConfig = _configuration.GetSection("Monitoring:NotificationChannels:Email");
        channels.Add(new NotificationChannel
        {
            Type = "Email",
            IsEnabled = emailConfig.GetValue<bool>("Enabled", false),
            Priority = emailConfig.GetValue<int>("Priority", 2),
            Configuration = new Dictionary<string, string>
            {
                ["SmtpServer"] = emailConfig.GetValue<string>("SmtpServer") ?? "",
                ["SmtpPort"] = emailConfig.GetValue<string>("SmtpPort") ?? "587",
                ["ToAddress"] = emailConfig.GetValue<string>("ToAddress") ?? ""
            }
        });

        // Check SMS
        var smsConfig = _configuration.GetSection("Monitoring:NotificationChannels:SMS");
        channels.Add(new NotificationChannel
        {
            Type = "SMS",
            IsEnabled = smsConfig.GetValue<bool>("Enabled", false),
            Priority = smsConfig.GetValue<int>("Priority", 1),
            Configuration = new Dictionary<string, string>
            {
                ["Provider"] = smsConfig.GetValue<string>("Provider") ?? "Twilio",
                ["ToNumber"] = smsConfig.GetValue<string>("ToNumber") ?? ""
            }
        });

        // Check Slack
        var slackConfig = _configuration.GetSection("Monitoring:NotificationChannels:Slack");
        channels.Add(new NotificationChannel
        {
            Type = "Slack",
            IsEnabled = slackConfig.GetValue<bool>("Enabled", false),
            Priority = slackConfig.GetValue<int>("Priority", 3),
            Configuration = new Dictionary<string, string>
            {
                ["WebhookUrl"] = slackConfig.GetValue<string>("WebhookUrl") ?? "",
                ["Channel"] = slackConfig.GetValue<string>("Channel") ?? "#alerts"
            }
        });

        return channels;
    }

    public async Task<bool> ValidateNotificationConfigurationAsync()
    {
        try
        {
            var channels = await GetAvailableChannelsAsync();
            var hasValidChannel = false;

            foreach (var channel in channels.Where(c => c.IsEnabled))
            {
                switch (channel.Type.ToLower())
                {
                    case "email":
                        if (ValidateEmailConfiguration(channel.Configuration))
                        {
                            hasValidChannel = true;
                            _logger.LogInformation("Email notification configuration is valid");
                        }
                        else
                        {
                            _logger.LogWarning("Email notification configuration is invalid");
                        }
                        break;
                    case "sms":
                        if (ValidateSmsConfiguration(channel.Configuration))
                        {
                            hasValidChannel = true;
                            _logger.LogInformation("SMS notification configuration is valid");
                        }
                        else
                        {
                            _logger.LogWarning("SMS notification configuration is invalid");
                        }
                        break;
                    case "slack":
                        if (ValidateSlackConfiguration(channel.Configuration))
                        {
                            hasValidChannel = true;
                            _logger.LogInformation("Slack notification configuration is valid");
                        }
                        else
                        {
                            _logger.LogWarning("Slack notification configuration is invalid");
                        }
                        break;
                }
            }

            return hasValidChannel;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating notification configuration");
            return false;
        }
    }

    // Private helper methods
    private string BuildEmailBody(RiskAlert alert)
    {
        var sb = new StringBuilder();
        sb.AppendLine("<html><body>");
        sb.AppendLine("<h2>Zero DTE Trading System Alert</h2>");
        sb.AppendLine($"<p><strong>Alert Type:</strong> {alert.Type}</p>");
        sb.AppendLine($"<p><strong>Severity:</strong> <span style='color: {GetSeverityColor(alert.Severity)}'>{alert.Severity}</span></p>");
        sb.AppendLine($"<p><strong>Message:</strong> {alert.Message}</p>");
        sb.AppendLine($"<p><strong>Timestamp:</strong> {alert.Timestamp:yyyy-MM-dd HH:mm:ss} UTC</p>");
        sb.AppendLine($"<p><strong>Current Value:</strong> {alert.Value:F2}</p>");
        sb.AppendLine($"<p><strong>Threshold:</strong> {alert.Threshold:F2}</p>");
        sb.AppendLine("<hr>");
        sb.AppendLine("<p><em>This is an automated alert from the Zero DTE Trading System.</em></p>");
        sb.AppendLine("</body></html>");
        return sb.ToString();
    }

    private string GetSeverityColor(RiskLevel severity)
    {
        return severity switch
        {
            RiskLevel.Low => "green",
            RiskLevel.Medium => "orange",
            RiskLevel.High => "red",
            RiskLevel.Critical => "darkred",
            _ => "black"
        };
    }

    private async Task<bool> SendTwilioSmsAsync(RiskAlert alert, IConfigurationSection config)
    {
        // Placeholder for Twilio SMS implementation
        // In a real implementation, you would use the Twilio SDK
        _logger.LogInformation($"Twilio SMS would be sent for alert: {alert.Type}");
        await Task.Delay(100); // Simulate API call
        return true;
    }

    private async Task<bool> SendAwsSmsAsync(RiskAlert alert, IConfigurationSection config)
    {
        // Placeholder for AWS SNS SMS implementation
        // In a real implementation, you would use the AWS SDK
        _logger.LogInformation($"AWS SNS SMS would be sent for alert: {alert.Type}");
        await Task.Delay(100); // Simulate API call
        return true;
    }

    private string BuildSlackPayload(RiskAlert alert)
    {
        var color = alert.Severity switch
        {
            RiskLevel.Low => "good",
            RiskLevel.Medium => "warning",
            RiskLevel.High => "danger",
            RiskLevel.Critical => "danger",
            _ => "warning"
        };

        return $@"{{
            ""text"": ""Zero DTE Trading Alert"",
            ""attachments"": [
                {{
                    ""color"": ""{color}"",
                    ""title"": ""{alert.Type} Alert"",
                    ""text"": ""{alert.Message}"",
                    ""fields"": [
                        {{
                            ""title"": ""Severity"",
                            ""value"": ""{alert.Severity}"",
                            ""short"": true
                        }},
                        {{
                            ""title"": ""Timestamp"",
                            ""value"": ""{alert.Timestamp:yyyy-MM-dd HH:mm:ss} UTC"",
                            ""short"": true
                        }},
                        {{
                            ""title"": ""Current Value"",
                            ""value"": ""{alert.Value:F2}"",
                            ""short"": true
                        }},
                        {{
                            ""title"": ""Threshold"",
                            ""value"": ""{alert.Threshold:F2}"",
                            ""short"": true
                        }}
                    ]
                }}
            ]
        }}";
    }

    private bool ValidateEmailConfiguration(Dictionary<string, string> config)
    {
        return config.ContainsKey("SmtpServer") && !string.IsNullOrEmpty(config["SmtpServer"]) &&
               config.ContainsKey("ToAddress") && !string.IsNullOrEmpty(config["ToAddress"]);
    }

    private bool ValidateSmsConfiguration(Dictionary<string, string> config)
    {
        return config.ContainsKey("Provider") && !string.IsNullOrEmpty(config["Provider"]) &&
               config.ContainsKey("ToNumber") && !string.IsNullOrEmpty(config["ToNumber"]);
    }

    private bool ValidateSlackConfiguration(Dictionary<string, string> config)
    {
        return config.ContainsKey("WebhookUrl") && !string.IsNullOrEmpty(config["WebhookUrl"]);
    }
}
