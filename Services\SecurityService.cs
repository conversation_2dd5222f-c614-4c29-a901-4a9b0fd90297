using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace ZeroDateStrat.Services;

public interface ISecurityService
{
    Task<string> EncryptSensitiveDataAsync(string data);
    Task<string> DecryptSensitiveDataAsync(string encryptedData);
    Task<bool> ValidateApiKeyFormatAsync(string apiKey);
    Task<bool> ValidateSecretKeyFormatAsync(string secretKey);
    Task<string> GetSecureApiKeyAsync();
    Task<string> GetSecureSecretKeyAsync();
    Task<bool> IsRunningInSecureEnvironmentAsync();
    Task<SecurityAuditResult> PerformSecurityAuditAsync();
    Task LogSecurityEventAsync(SecurityEvent securityEvent);
}

public class SecurityService : ISecurityService
{
    private readonly ILogger<SecurityService> _logger;
    private readonly IConfiguration _configuration;
    private readonly string _encryptionKey;
    private readonly List<SecurityEvent> _securityEvents = new();

    public SecurityService(ILogger<SecurityService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        
        // In production, this should come from a secure key management service
        _encryptionKey = Environment.GetEnvironmentVariable("ENCRYPTION_KEY") ?? 
                        GenerateDefaultEncryptionKey();
    }

    public async Task<string> EncryptSensitiveDataAsync(string data)
    {
        try
        {
            if (string.IsNullOrEmpty(data))
                return string.Empty;

            using var aes = Aes.Create();
            aes.Key = Encoding.UTF8.GetBytes(_encryptionKey.PadRight(32).Substring(0, 32));
            aes.GenerateIV();

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using var swEncrypt = new StreamWriter(csEncrypt);

            await swEncrypt.WriteAsync(data);
            swEncrypt.Close();

            var encrypted = msEncrypt.ToArray();
            var result = Convert.ToBase64String(aes.IV.Concat(encrypted).ToArray());

            await LogSecurityEventAsync(new SecurityEvent
            {
                EventType = SecurityEventType.DataEncryption,
                Message = "Sensitive data encrypted",
                Timestamp = DateTime.UtcNow
            });

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error encrypting sensitive data");
            await LogSecurityEventAsync(new SecurityEvent
            {
                EventType = SecurityEventType.EncryptionFailure,
                Message = $"Encryption failed: {ex.Message}",
                Timestamp = DateTime.UtcNow,
                Severity = SecuritySeverity.High
            });
            throw;
        }
    }

    public async Task<string> DecryptSensitiveDataAsync(string encryptedData)
    {
        try
        {
            if (string.IsNullOrEmpty(encryptedData))
                return string.Empty;

            var fullCipher = Convert.FromBase64String(encryptedData);
            
            using var aes = Aes.Create();
            aes.Key = Encoding.UTF8.GetBytes(_encryptionKey.PadRight(32).Substring(0, 32));
            
            var iv = fullCipher.Take(16).ToArray();
            var cipher = fullCipher.Skip(16).ToArray();
            
            aes.IV = iv;

            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(cipher);
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);

            var result = await srDecrypt.ReadToEndAsync();

            await LogSecurityEventAsync(new SecurityEvent
            {
                EventType = SecurityEventType.DataDecryption,
                Message = "Sensitive data decrypted",
                Timestamp = DateTime.UtcNow
            });

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error decrypting sensitive data");
            await LogSecurityEventAsync(new SecurityEvent
            {
                EventType = SecurityEventType.DecryptionFailure,
                Message = $"Decryption failed: {ex.Message}",
                Timestamp = DateTime.UtcNow,
                Severity = SecuritySeverity.High
            });
            throw;
        }
    }

    public async Task<bool> ValidateApiKeyFormatAsync(string apiKey)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(apiKey))
                return false;

            // Alpaca API keys typically start with "AK" and are 20 characters long
            var isValid = apiKey.StartsWith("AK") && apiKey.Length == 20 && 
                         apiKey.All(c => char.IsLetterOrDigit(c));

            if (!isValid)
            {
                await LogSecurityEventAsync(new SecurityEvent
                {
                    EventType = SecurityEventType.InvalidApiKey,
                    Message = "Invalid API key format detected",
                    Timestamp = DateTime.UtcNow,
                    Severity = SecuritySeverity.Medium
                });
            }

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating API key format");
            return false;
        }
    }

    public async Task<bool> ValidateSecretKeyFormatAsync(string secretKey)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(secretKey))
                return false;

            // Alpaca secret keys are typically 40 characters long
            var isValid = secretKey.Length == 40 && 
                         secretKey.All(c => char.IsLetterOrDigit(c));

            if (!isValid)
            {
                await LogSecurityEventAsync(new SecurityEvent
                {
                    EventType = SecurityEventType.InvalidSecretKey,
                    Message = "Invalid secret key format detected",
                    Timestamp = DateTime.UtcNow,
                    Severity = SecuritySeverity.Medium
                });
            }

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating secret key format");
            return false;
        }
    }

    public async Task<string> GetSecureApiKeyAsync()
    {
        try
        {
            var apiKey = _configuration["Alpaca:ApiKey"];
            
            // Check if the key is encrypted (starts with encrypted prefix)
            if (apiKey?.StartsWith("ENC:") == true)
            {
                var encryptedKey = apiKey.Substring(4);
                return await DecryptSensitiveDataAsync(encryptedKey);
            }

            // Log warning if using unencrypted key
            if (!string.IsNullOrEmpty(apiKey))
            {
                await LogSecurityEventAsync(new SecurityEvent
                {
                    EventType = SecurityEventType.UnencryptedCredentials,
                    Message = "API key is stored in plain text",
                    Timestamp = DateTime.UtcNow,
                    Severity = SecuritySeverity.Medium
                });
            }

            return apiKey ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving secure API key");
            throw;
        }
    }

    public async Task<string> GetSecureSecretKeyAsync()
    {
        try
        {
            var secretKey = _configuration["Alpaca:SecretKey"];
            
            // Check if the key is encrypted (starts with encrypted prefix)
            if (secretKey?.StartsWith("ENC:") == true)
            {
                var encryptedKey = secretKey.Substring(4);
                return await DecryptSensitiveDataAsync(encryptedKey);
            }

            // Log warning if using unencrypted key
            if (!string.IsNullOrEmpty(secretKey))
            {
                await LogSecurityEventAsync(new SecurityEvent
                {
                    EventType = SecurityEventType.UnencryptedCredentials,
                    Message = "Secret key is stored in plain text",
                    Timestamp = DateTime.UtcNow,
                    Severity = SecuritySeverity.Medium
                });
            }

            return secretKey ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving secure secret key");
            throw;
        }
    }

    public async Task<bool> IsRunningInSecureEnvironmentAsync()
    {
        try
        {
            var checks = new List<bool>
            {
                // Check if running in production environment
                Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != "Development",
                
                // Check if encryption key is set
                !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("ENCRYPTION_KEY")),
                
                // Check if using HTTPS URLs
                _configuration["Alpaca:BaseUrl"]?.StartsWith("https://") == true,
                
                // Check if credentials are encrypted
                _configuration["Alpaca:ApiKey"]?.StartsWith("ENC:") == true,
                _configuration["Alpaca:SecretKey"]?.StartsWith("ENC:") == true
            };

            var secureEnvironment = checks.Count(c => c) >= 3; // At least 3 out of 5 checks pass

            await LogSecurityEventAsync(new SecurityEvent
            {
                EventType = SecurityEventType.SecurityAudit,
                Message = $"Security environment check: {(secureEnvironment ? "SECURE" : "INSECURE")}",
                Timestamp = DateTime.UtcNow,
                Severity = secureEnvironment ? SecuritySeverity.Low : SecuritySeverity.High
            });

            return secureEnvironment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking secure environment");
            return false;
        }
    }

    public async Task<SecurityAuditResult> PerformSecurityAuditAsync()
    {
        try
        {
            _logger.LogInformation("Performing comprehensive security audit");

            var result = new SecurityAuditResult
            {
                AuditTimestamp = DateTime.UtcNow,
                Checks = new List<SecurityCheck>()
            };

            // Check API key security
            var apiKey = await GetSecureApiKeyAsync();
            result.Checks.Add(new SecurityCheck
            {
                CheckName = "API Key Security",
                Passed = await ValidateApiKeyFormatAsync(apiKey) && apiKey.StartsWith("AK"),
                Message = "API key format and security validation"
            });

            // Check secret key security
            var secretKey = await GetSecureSecretKeyAsync();
            result.Checks.Add(new SecurityCheck
            {
                CheckName = "Secret Key Security",
                Passed = await ValidateSecretKeyFormatAsync(secretKey),
                Message = "Secret key format and security validation"
            });

            // Check environment security
            result.Checks.Add(new SecurityCheck
            {
                CheckName = "Environment Security",
                Passed = await IsRunningInSecureEnvironmentAsync(),
                Message = "Overall environment security assessment"
            });

            // Check configuration security
            var configSecure = !_configuration["Alpaca:ApiKey"]?.Contains("YOUR_") == true &&
                              !_configuration["Alpaca:SecretKey"]?.Contains("YOUR_") == true;
            result.Checks.Add(new SecurityCheck
            {
                CheckName = "Configuration Security",
                Passed = configSecure,
                Message = "Configuration contains valid credentials"
            });

            result.OverallSecurityScore = (decimal)result.Checks.Count(c => c.Passed) / result.Checks.Count;
            result.IsSecure = result.OverallSecurityScore >= 0.75m;

            _logger.LogInformation($"Security audit completed. Score: {result.OverallSecurityScore:P0}, Secure: {result.IsSecure}");

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing security audit");
            throw;
        }
    }

    public async Task LogSecurityEventAsync(SecurityEvent securityEvent)
    {
        try
        {
            _securityEvents.Add(securityEvent);

            // Keep only last 1000 events
            if (_securityEvents.Count > 1000)
            {
                _securityEvents.RemoveRange(0, _securityEvents.Count - 1000);
            }

            var logLevel = securityEvent.Severity switch
            {
                SecuritySeverity.Low => LogLevel.Information,
                SecuritySeverity.Medium => LogLevel.Warning,
                SecuritySeverity.High => LogLevel.Error,
                SecuritySeverity.Critical => LogLevel.Critical,
                _ => LogLevel.Information
            };

            _logger.Log(logLevel, "Security Event: {EventType} - {Message}", 
                       securityEvent.EventType, securityEvent.Message);

            // In production, you might want to send critical events to external monitoring
            if (securityEvent.Severity >= SecuritySeverity.High)
            {
                // TODO: Send to external security monitoring system
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging security event");
        }
    }

    private string GenerateDefaultEncryptionKey()
    {
        // This is a fallback - in production, use proper key management
        var key = Environment.MachineName + Environment.UserName + "ZeroDateStrat";
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(key));
        return Convert.ToBase64String(hash);
    }
}

public class SecurityEvent
{
    public SecurityEventType EventType { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public SecuritySeverity Severity { get; set; } = SecuritySeverity.Low;
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public enum SecurityEventType
{
    DataEncryption,
    DataDecryption,
    EncryptionFailure,
    DecryptionFailure,
    InvalidApiKey,
    InvalidSecretKey,
    UnencryptedCredentials,
    SecurityAudit,
    UnauthorizedAccess,
    ConfigurationChange
}

public enum SecuritySeverity
{
    Low,
    Medium,
    High,
    Critical
}

public class SecurityAuditResult
{
    public DateTime AuditTimestamp { get; set; }
    public List<SecurityCheck> Checks { get; set; } = new();
    public decimal OverallSecurityScore { get; set; }
    public bool IsSecure { get; set; }
}

public class SecurityCheck
{
    public string CheckName { get; set; } = string.Empty;
    public bool Passed { get; set; }
    public string Message { get; set; } = string.Empty;
}
