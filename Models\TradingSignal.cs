namespace ZeroDateStrat.Models;

public interface IPosition
{
    string Symbol { get; set; }
    int Quantity { get; set; }
    double MarketValue { get; set; }
}

public class TradingSignal
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Strategy { get; set; } = string.Empty;
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public SignalType Type { get; set; }
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public DateTime ExpirationDate { get; set; }
    public decimal Confidence { get; set; }
    public decimal ExpectedProfit { get; set; }
    public decimal MaxLoss { get; set; }
    public decimal MaxRisk { get; set; }
    public List<OptionLeg> Legs { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string Reason { get; set; } = string.Empty;
    public bool IsExecuted { get; set; }
    public DateTime? ExecutedAt { get; set; }

    public decimal RiskRewardRatio { get; set; }
    public decimal CalculatedRiskRewardRatio => MaxLoss != 0 ? ExpectedProfit / Math.Abs(MaxLoss) : 0;
    public bool IsValid => Legs.Any() && Confidence > 0.3m && RiskRewardRatio > 0.01m; // More realistic for credit spreads
}

public class OptionLeg
{
    public string Symbol { get; set; } = string.Empty;
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public OptionType OptionType { get; set; }
    public decimal StrikePrice { get; set; }
    public DateTime ExpirationDate { get; set; }
    public OrderSide Side { get; set; }
    public int Quantity { get; set; }
    public decimal Price { get; set; }
    public decimal Delta { get; set; }
    public decimal Gamma { get; set; }
    public decimal Theta { get; set; }
    public decimal Vega { get; set; }
}

public enum SignalType
{
    IronCondor,
    IronButterfly,
    Strangle,
    Straddle,
    CallSpread,
    PutSpread,
    SingleOption
}

public enum OrderSide
{
    Buy,
    Sell
}

public class Position
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Strategy { get; set; } = string.Empty;
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public List<OptionLeg> Legs { get; set; } = new();
    public decimal OpenCredit { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal UnrealizedPnL { get; set; }
    public DateTime OpenedAt { get; set; }
    public DateTime? ClosedAt { get; set; }
    public PositionStatus Status { get; set; } = PositionStatus.Open;
    public decimal ProfitTarget { get; set; }
    public decimal StopLoss { get; set; }
    public DateTime ExpirationDate { get; set; }

    public decimal PnLPercentage => OpenCredit != 0 ? (UnrealizedPnL / OpenCredit) * 100 : 0;
    public bool ShouldClose => Status == PositionStatus.Open && 
                              (UnrealizedPnL >= ProfitTarget || 
                               UnrealizedPnL <= -StopLoss ||
                               DateTime.UtcNow.Date >= ExpirationDate.Date);
}

public enum PositionStatus
{
    Open,
    Closed,
    Adjusted,
    Expired
}

// Backtesting Models
public class BacktestTrade
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Strategy { get; set; } = string.Empty;
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public DateTime EntryTime { get; set; }
    public DateTime? ExitTime { get; set; }
    public decimal EntryPrice { get; set; }
    public decimal? ExitPrice { get; set; }
    public decimal Quantity { get; set; }
    public decimal Commission { get; set; }
    public decimal RealizedPnL { get; set; }
    public decimal MaxDrawdown { get; set; }
    public decimal MaxProfit { get; set; }
    public TimeSpan HoldingPeriod => ExitTime?.Subtract(EntryTime) ?? TimeSpan.Zero;
    public string ExitReason { get; set; } = string.Empty;
    public List<OptionLeg> Legs { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class BacktestResult
{
    public string StrategyName { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal InitialCapital { get; set; }
    public decimal FinalCapital { get; set; }
    public decimal TotalReturn { get; set; }
    public decimal AnnualizedReturn { get; set; }
    public decimal MaxDrawdown { get; set; }
    public decimal SharpeRatio { get; set; }
    public decimal SortinoRatio { get; set; }
    public decimal CalmarRatio { get; set; }
    public int TotalTrades { get; set; }
    public int WinningTrades { get; set; }
    public int LosingTrades { get; set; }
    public decimal WinRate => TotalTrades > 0 ? (decimal)WinningTrades / TotalTrades : 0;
    public decimal AverageWin { get; set; }
    public decimal AverageLoss { get; set; }
    public decimal ProfitFactor => Math.Abs(AverageLoss) > 0 ? AverageWin / Math.Abs(AverageLoss) : 0;
    public decimal LargestWin { get; set; }
    public decimal LargestLoss { get; set; }
    public TimeSpan AverageHoldingPeriod { get; set; }
    public List<BacktestTrade> Trades { get; set; } = new();
    public List<DailyPerformance> DailyPerformance { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class DailyPerformance
{
    public DateTime Date { get; set; }
    public decimal PortfolioValue { get; set; }
    public decimal DailyReturn { get; set; }
    public decimal CumulativeReturn { get; set; }
    public decimal Drawdown { get; set; }
    public int ActivePositions { get; set; }
    public decimal DailyPnL { get; set; }
}
