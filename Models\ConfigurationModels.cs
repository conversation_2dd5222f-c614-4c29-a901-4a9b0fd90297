using System.ComponentModel.DataAnnotations;

namespace ZeroDateStrat.Models;

public class TradingConfiguration
{
    [Required]
    public string PrimarySymbol { get; set; } = "SPX";
    
    [Required]
    public string BackupSymbol { get; set; } = "SPY";
    
    [Range(1000, 1000000)]
    public decimal MaxPositionSize { get; set; } = 10000;
    
    [Range(100, 10000)]
    public decimal MaxDailyLoss { get; set; } = 500;
    
    [Range(0.01, 0.1)]
    public decimal RiskPerTrade { get; set; } = 0.02m;
    
    [Range(1, 20)]
    public int MaxPositionsPerDay { get; set; } = 5;
    
    [Range(0, 7)]
    public int MinDaysToExpiration { get; set; } = 0;
    
    [Range(0, 7)]
    public int MaxDaysToExpiration { get; set; } = 0;
    
    [Required]
    public string EntryTimeStart { get; set; } = "09:45:00";
    
    [Required]
    public string EntryTimeEnd { get; set; } = "10:30:00";
    
    [Required]
    public string ManagementTime { get; set; } = "14:00:00";
    
    [Required]
    public string ForceCloseTime { get; set; } = "15:45:00";
    
    [Required]
    public string TradingEndTime { get; set; } = "16:00:00";
    
    [Range(1000, 100000)]
    public decimal MinAccountEquity { get; set; } = 2000;
    
    [Range(500, 50000)]
    public decimal MinBuyingPower { get; set; } = 1000;
    
    [Range(0.1, 2.0)]
    public decimal ProfitTargetPercent { get; set; } = 0.5m;
    
    [Range(0.5, 5.0)]
    public decimal StopLossPercent { get; set; } = 2.0m;
    
    [Range(0.1, 1.0)]
    public decimal RiskRewardThreshold { get; set; } = 0.15m;
}

public class AlpacaConfiguration
{
    [Required]
    public string ApiKey { get; set; } = string.Empty;
    
    [Required]
    public string SecretKey { get; set; } = string.Empty;
    
    [Required]
    [Url]
    public string BaseUrl { get; set; } = "https://api.alpaca.markets";
    
    [Required]
    [Url]
    public string DataUrl { get; set; } = "https://data.alpaca.markets";
}

public class RiskConfiguration
{
    [Range(0.01, 0.2)]
    public decimal MaxDrawdown { get; set; } = 0.08m;
    
    [Range(0.01, 0.1)]
    public decimal VaRLimit { get; set; } = 0.03m;
    
    [Range(0.1, 1.0)]
    public decimal MaxConcentration { get; set; } = 0.6m;
    
    [Range(0.1, 1.0)]
    public decimal MaxCorrelatedExposure { get; set; } = 0.7m;
    
    [Range(0.1, 1.0)]
    public decimal PortfolioHeatLimit { get; set; } = 0.75m;
    
    [Range(1, 50)]
    public int MaxDailyTrades { get; set; } = 8;
    
    [Range(1, 50)]
    public int MaxOpenPositions { get; set; } = 12;
    
    [Range(1.0, 3.0)]
    public decimal StressTestMultiplier { get; set; } = 1.5m;
    
    [Range(0.1, 1.0)]
    public decimal RiskRewardMinimum { get; set; } = 0.15m;
    
    [Range(1, 10)]
    public int MaxPositionsPerSymbol { get; set; } = 4;
    
    [Range(0.1, 1.0)]
    public decimal ConcentrationWarningLevel { get; set; } = 0.5m;
}

public class MarketRegimeConfiguration
{
    [Range(10, 50)]
    public decimal VixLowThreshold { get; set; } = 18;
    
    [Range(20, 80)]
    public decimal VixHighThreshold { get; set; } = 25;
    
    [Range(5, 50)]
    public int TrendLookbackPeriods { get; set; } = 20;
    
    [Range(5, 30)]
    public int VolatilityLookbackPeriods { get; set; } = 14;
    
    [Range(10, 100)]
    public int VolatilityCalculationDays { get; set; } = 30;
    
    [Range(20, 200)]
    public int VolatilityForecastLookback { get; set; } = 60;
    
    [Range(5, 30)]
    public int ATRPeriods { get; set; } = 14;
    
    [Range(5, 30)]
    public int RSIPeriods { get; set; } = 14;
    
    [Range(10, 50)]
    public int BollingerBandPeriods { get; set; } = 20;
    
    [Range(1.0, 3.0)]
    public decimal BollingerBandStdDev { get; set; } = 2.0m;
    
    [Range(0.01, 0.5)]
    public decimal GarchAlpha { get; set; } = 0.1m;
    
    [Range(0.5, 0.99)]
    public decimal GarchBeta { get; set; } = 0.85m;
    
    [Range(0.000001, 0.001)]
    public decimal GarchOmega { get; set; } = 0.00001m;
    
    [Range(1, 24)]
    public int MicrostructureLookbackHours { get; set; } = 6;
    
    public bool MultiTimeframeEnabled { get; set; } = true;
    
    [Range(0.1, 1.0)]
    public decimal RegimeTransitionSensitivity { get; set; } = 0.7m;
    
    [Range(1.0, 5.0)]
    public decimal VolatilitySpikeThreshold { get; set; } = 1.5m;
    
    [Range(0.1, 1.0)]
    public decimal CorrelationBreakdownThreshold { get; set; } = 0.3m;
    
    [Range(2.0, 10.0)]
    public decimal UnusualActivityVolumeThreshold { get; set; } = 5.0m;
    
    [Range(30, 90)]
    public int SentimentExtremeThreshold { get; set; } = 60;
    
    [Range(5, 50)]
    public int MarketBreadthLookbackDays { get; set; } = 20;
    
    [Range(1, 12)]
    public int OptionsFlowLookbackHours { get; set; } = 4;
}

public class MachineLearningConfiguration
{
    [Range(1, 168)]
    public int ModelUpdateIntervalHours { get; set; } = 24;
    
    [Range(50, 10000)]
    public int MinTrainingDataPoints { get; set; } = 100;
    
    [Range(0.5, 0.99)]
    public decimal ConfidenceThreshold { get; set; } = 0.7m;
    
    public SignalQualityWeights SignalQualityWeights { get; set; } = new();
    public PredictionTimeframes PredictionTimeframes { get; set; } = new();
}

public class SignalQualityWeights
{
    [Range(0.0, 1.0)]
    public decimal ML { get; set; } = 0.4m;
    
    [Range(0.0, 1.0)]
    public decimal Technical { get; set; } = 0.3m;
    
    [Range(0.0, 1.0)]
    public decimal MarketCondition { get; set; } = 0.2m;
    
    [Range(0.0, 1.0)]
    public decimal Liquidity { get; set; } = 0.1m;
}

public class PredictionTimeframes
{
    [Required]
    public string PriceDirection { get; set; } = "1h";
    
    [Required]
    public string Volatility { get; set; } = "4h";
}

public class MonitoringConfiguration
{
    [Range(1000, 60000)]
    public int UpdateIntervalMs { get; set; } = 5000;
    
    [Range(5000, 120000)]
    public int AlertCheckIntervalMs { get; set; } = 10000;
    
    [Range(10000, 300000)]
    public int HealthCheckIntervalMs { get; set; } = 30000;
    
    [Range(100, 10000)]
    public int MaxMetricsHistory { get; set; } = 1000;
    
    public NotificationChannels NotificationChannels { get; set; } = new();
}

public class NotificationChannels
{
    public ConfigNotificationChannel Console { get; set; } = new() { Enabled = true, Priority = 1 };
    public EmailNotificationChannel Email { get; set; } = new();
    public SmsNotificationChannel SMS { get; set; } = new();
    public SlackNotificationChannel Slack { get; set; } = new();
}

public class ConfigNotificationChannel
{
    public bool Enabled { get; set; }

    [Range(1, 10)]
    public int Priority { get; set; } = 1;
}

public class EmailNotificationChannel : ConfigNotificationChannel
{
    public string SmtpServer { get; set; } = string.Empty;

    [Range(1, 65535)]
    public int SmtpPort { get; set; } = 587;

    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;

    [EmailAddress]
    public string ToAddress { get; set; } = string.Empty;

    public bool UseSsl { get; set; } = true;
}

public class SmsNotificationChannel : ConfigNotificationChannel
{
    public string Provider { get; set; } = "Twilio"; // Twilio, AWS, etc.
    public string AccountSid { get; set; } = string.Empty;
    public string AuthToken { get; set; } = string.Empty;
    public string FromNumber { get; set; } = string.Empty;
    public string ToNumber { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty; // For AWS SNS
}

public class SlackNotificationChannel : ConfigNotificationChannel
{
    public string WebhookUrl { get; set; } = string.Empty;
    public string Channel { get; set; } = "#alerts";
    public string Username { get; set; } = "Zero DTE Bot";
    public string IconEmoji { get; set; } = ":warning:";
}
