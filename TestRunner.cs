using ZeroDateStrat.Tests;

namespace ZeroDateStrat;

public class TestRunner
{
    public static void RunTests()
    {
        Console.WriteLine("=== ZeroDateStrat Test Runner ===\n");

        try
        {
            // Run basic model tests
            BasicTests.RunAllTests();

            Console.WriteLine("\n=== All Tests Completed Successfully ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n=== Test Failed ===");
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack Trace: {ex.StackTrace}");
        }
    }
}
